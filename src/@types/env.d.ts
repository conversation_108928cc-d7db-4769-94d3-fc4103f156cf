/// <reference types="node" />

declare namespace NodeJS {
  interface ProcessEnv {
    PORT: number;
    REQUEST_TIMEOUT: number;
    // Swagger Config
    SWAGGER_TITLE: string;
    SWAGGER_DESCRIPTION: string;
    SWAGGER_VERSION: string;
    // DB Config
    DB_PRIMARY_TYPE: 'postgres' | 'mssql' | 'mysql';
    DB_PRIMARY_HOST: string;
    DB_PRIMARY_PORT: number;s
    DB_PRIMARY_USERNAME: string;
    DB_PRIMARY_PASSWORD: string;
    DB_PRIMARY_DATABASE: string;
    DB_PRIMARY_SYNCHRONIZE: boolean;
    DB_PRIMARY_SSL: boolean;
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: boolean;
    // JWT HS256 config
    JWT_SECRET: string;
    JWT_EXPIRY: string;
    JWT_REFRESH_TOKEN_SECRET: string;
    JWT_REFRESH_TOKEN_EXPIRY: string;
    EXTERNAL_API_HOST: string;
    EXTERNAL_API_PATH: string;
    GOOGLE_CLIENT_ID: string;
    GOOGLE_CLIENT_SECRET: string;
    GOOGLE_CALLBACK_URL: string;
    // Stripe Payment
    STRIPE_SECRET_KEY: string;
    STRIPE_WEBHOOK_SECRET: string;
    STRIPE_PRODUCT_SUBSCRIPTION_ID: string;
    // AWS S3
    AWS_S3_ACCESS_KEY_ID: string;
    AWS_S3_SECRET_ACCESS_KEY: string;
    AWS_S3_BUCKET_NAME: string;
    REDIRECT_URL_PAYMENT: string;
  }
}
