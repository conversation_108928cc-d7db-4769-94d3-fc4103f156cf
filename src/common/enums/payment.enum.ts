export namespace NSPayment {
    export enum ETransactionStatus {
        PENDING = 'PENDING', // Đang chờ xử lý: Giao dịch đã được tạo nhưng chưa hoàn tất hoặc chưa xác nhận
        COMPLETED = 'COMPLETED', // Hoàn tất: Giao dịch đã thành công và tiền đã được chuyển
        FAILED = 'FAILED', // Thất bại: Giao dịch không thành công do lỗi hệ thống, thẻ bị từ chối hoặc lỗi khác
        CANCELED = 'CANCELED', // Đã hủy: Giao dịch đã bị hủy bởi người dùng hoặc hệ thống trước khi hoàn tất
        REFUNDED = 'REFUNDED', // Đã hoàn tiền: Số tiền của giao dịch đã được hoàn lại cho người dùng
        EXPIRED = 'EXPIRED', // Hết hạn: <PERSON>ia<PERSON> dịch không được hoàn tất trong thời gian quy định và tự động hết hiệu lực
        IN_PROGRESS = 'IN_PROGRESS', // Đang xử lý: Giao dịch đang được xử lý và có thể mất thời gian để hoàn tất
        ON_HOLD = 'ON_HOLD', // Tạm giữ: Giao dịch bị tạm dừng do các vấn đề cần xem xét thêm (như xác minh tài khoản)
    }

    // Đối tác cổng thanh toán    
    export enum EPaymentProvider {
        SEPAY = 'SEPAY',
        STRIPE = 'STRIPE',
        Momo = 'momo',
        VNPay = 'vnpay',
    }

    // Hình thức thanh toán
    export enum EPaymentMethod {
        PAY_CARD = 'PAY_CARD',
        BANK_TRANSFER = 'BANK_TRANSFER',
    }
}