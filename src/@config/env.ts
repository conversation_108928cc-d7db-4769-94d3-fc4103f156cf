import { stringToBoolean } from '~/utils/parser.util';

export function configEnv() {
  return {
    PORT: Number(process.env.PORT),
    TZ: process.env.TZ,
    REQUEST_TIMEOUT: Number(process.env.REQUEST_TIMEOUT),
    DB_PRIMARY_TYPE: process.env.DB_PRIMARY_TYPE,
    DB_PRIMARY_HOST: process.env.DB_PRIMARY_HOST,
    DB_PRIMARY_PORT: Number(process.env.DB_PRIMARY_PORT),
    DB_PRIMARY_USERNAME: process.env.DB_PRIMARY_USERNAME,
    DB_PRIMARY_PASSWORD: process.env.DB_PRIMARY_PASSWORD,
    DB_PRIMARY_DATABASE: process.env.DB_PRIMARY_DATABASE,
    DB_PRIMARY_SYNCHRONIZE: stringToBoolean(process.env.DB_PRIMARY_SYNCHRONIZE),
    DB_PRIMARY_SSL: stringToBoolean(process.env.DB_PRIMARY_SSL),
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: stringToBoolean(
      process.env.DB_PRIMARY_SSL_REJECT_UNAUTHORIZED,
    ),
    // SWAGGER CONFIG
    SWAGGER_TITLE: process.env.SWAGGER_TITLE,
    SWAGGER_DESCRIPTION: process.env.SWAGGER_DESCRIPTION,
    SWAGGER_VERSION: process.env.SWAGGER_VERSION,
    JWT_SECRET: process.env.JWT_SECRET,
    JWT_EXPIRY: process.env.JWT_EXPIRY,
    JWT_REFRESH_TOKEN_SECRET: process.env.JWT_REFRESH_TOKEN_SECRET,
    JWT_REFRESH_TOKEN_EXPIRY: process.env.JWT_REFRESH_TOKEN_EXPIRY,
    EXTERNAL_API_HOST: process.env.EXTERNAL_API_HOST,
    EXTERNAL_API_PATH: process.env.EXTERNAL_API_PATH,
    GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID,
    GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET,
    GOOGLE_CALLBACK_URL: process.env.GOOGLE_CALLBACK_URL,
    STRIPE_SECRET_KEY: process.env.STRIPE_SECRET_KEY,
    STRIPE_WEBHOOK_SECRET: process.env.STRIPE_WEBHOOK_SECRET,
    STRIPE_PRODUCT_SUBSCRIPTION_ID: process.env.STRIPE_PRODUCT_SUBSCRIPTION_ID,
    AWS_S3_ACCESS_KEY_ID: process.env.AWS_S3_ACCESS_KEY_ID,
    AWS_S3_SECRET_ACCESS_KEY: process.env.AWS_S3_SECRET_ACCESS_KEY,
    AWS_S3_BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME,
    REDIRECT_URL_PAYMENT: process.env.REDIRECT_URL_PAYMENT,
  } as NodeJS.ProcessEnv;
}
