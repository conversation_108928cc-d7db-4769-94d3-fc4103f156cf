import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Column, Entity, Index } from "typeorm";
import { NSOrder } from "~/common/enums/order.enum";

@Entity('order')
export class OrderEntity extends PrimaryBaseEntity {
    @ApiProperty({ description: 'Mã đơn' })
    @Column({ type: 'varchar', nullable: true })
    @Index()
    code: string;

    @ApiProperty({ description: 'ID của thành viên' })
    @Column({ type: 'uuid' })
    @Index()
    memberId: string;

    // Loại đơn hàng
    @Column({ type: 'varchar', length: 20, default: 'PACKAGE' })
    orderType: string;

    @ApiProperty({ description: '<PERSON>i<PERSON> bán' })
    @Column({ type: 'numeric', precision: 15, scale: 2, default: 0 })
    totalPrice: number;

    @ApiProperty({
        description: '<PERSON>ương thức thanh toán (nếu có)',
        enum: NSOrder.EPaymentMethod,
        example: NSOrder.EPaymentMethod.PAY_CARD,
    })
    @Column({ type: 'varchar', length: 255, default: NSOrder.EPaymentMethod.PAY_CARD })
    paymentMethod: NSOrder.EPaymentMethod;

    @ApiPropertyOptional({
        description: 'Trạng thái thanh toán',
        enum: NSOrder.EPaymentStatus,
        example: NSOrder.EPaymentStatus.PAID,
    })
    @Column({ type: 'varchar', nullable: true })
    paymentStatus: NSOrder.EPaymentStatus;

    @ApiPropertyOptional({ description: 'Ngày thanh toán' })
    @Column({ type: 'timestamptz', nullable: true })
    paymentDate?: Date;

    @ApiProperty({ description: 'Trạng thái' })
    @Column({ type: 'varchar', length: 20, default: NSOrder.EStatus.PENDING })
    status: NSOrder.EStatus;

    // Đơn hàng thanh toán tự động
    @ApiPropertyOptional({ description: 'Tự động gia hạn' })
    @Column({ type: 'boolean', default: false })
    isAutoRenewal?: boolean;

    @ApiPropertyOptional({ description: 'ID của subscription trên Stripe' })
    @Column({ type: 'varchar', nullable: true })
    subscriptionId?: string;
}
