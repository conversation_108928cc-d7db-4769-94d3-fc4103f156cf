import { DefController, DefPost } from "~/@core/decorator";
import { MemberPackageService } from "./member-package.service";
import { MemberPackageListDto } from "./dto/member-package.dto";
import { Body } from "@nestjs/common";

@DefController('member-package')
export class MemberPackageController {
    constructor(private readonly service: MemberPackageService) { }

    @DefPost('pagination', {
        summary: 'L<PERSON>y danh sách gói dịch vụ của member đã mua',
    })
    findPagination(@Body() body: MemberPackageListDto) {
        return this.service.findPagination(body);
    }

    @DefPost('find-all', {
        summary: '<PERSON><PERSON><PERSON> danh sách gói dịch vụ của member đã mua',
    })
    findAll() {
        return this.service.findAll();
    }

    @DefPost('detail', {
        summary: 'L<PERSON>y thông tin chi tiết gói dịch vụ của member',
    })
    findOne(@Body() body: string) {
        return this.service.findOne(body);
    }
}