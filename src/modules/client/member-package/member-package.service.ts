import { Injectable, NotFoundException } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import {
  MemberPackageRepo,
  OrderRepo,
  MemberApiLogRepo,
  PackagePlanRepo,
  PaymentTransactionRepo,
  MemberRepo,
  ConfigPackageRepo,
} from '~/domains/primary';
import { MemberPackageListDto } from './dto/member-package.dto';
import { Between, In } from 'typeorm';
import { memberSessionContext } from '../member-session.context';
import { NSMember } from '~/common/enums';

@Injectable()
export class MemberPackageService {
  constructor() { }

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  async findOne(id: string) {
    const packageMember = await this.memberPackageRepo.findOne({ where: { id } });
    if (!packageMember) throw new NotFoundException('Member package not found');

    const order = await this.orderRepo.findOne({ where: { id: packageMember.orderId } });
    if (!order) throw new NotFoundException('Order not found');

    const apiLogs = await this.memberApiLogRepo.find({
      where: { memberPackageId: packageMember.id },
    });

    return {
      package: packageMember,
      order,
      log: apiLogs,
    };
  }

  async findPagination(body: MemberPackageListDto) {
    const { memberId } = memberSessionContext;
    const { orderId, packagePlanId, status, expiredDateFrom, expiredDateTo, ...pageRequest } = body;
    const wheres: any = {};
    if (memberId) {
      wheres.memberId = memberId; // Update lại dùng memberSessionContext
    }
    if (orderId) {
      wheres.orderId = orderId;
    }
    if (packagePlanId) {
      wheres.packagePlanId = packagePlanId;
    }
    wheres.status = In([NSMember.EMemberPackageStatus.ACTIVE, NSMember.EMemberPackageStatus.EXPIRED]);
    if (status) {
      wheres.status = status;
    }
    if (expiredDateFrom && expiredDateTo) {
      wheres.expiredDate = Between(expiredDateFrom, expiredDateTo);
    }
    const { data, total } = await this.memberPackageRepo.findPagination(
      { where: wheres, order: { createdDate: 'DESC' } },
      pageRequest,
    );
    const plans = await this.packagePlanRepo.find();
    const mapping = data.map(item => {
      const plan = plans.find(p => p.id === item.packagePlanId);
      return {
        ...item,
        plan,
      };
    });
    return {
      data: mapping,
      total,
    };
  }

  async findAll() {
    const { memberId } = memberSessionContext;
    return this.memberPackageRepo.find({ where: { memberId, status: NSMember.EMemberPackageStatus.ACTIVE } });
  }
}
