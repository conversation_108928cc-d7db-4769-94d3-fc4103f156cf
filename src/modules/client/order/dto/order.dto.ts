import { ApiProperty } from '@nestjs/swagger';
import { NSOrder } from '~/common/enums/order.enum';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSPayment } from '~/common/enums/payment.enum';
import { NSPackage } from '~/common/enums';

export class OrderListDto extends PageRequest {

  @ApiProperty({ description: 'ID của thành viên' })
  @IsOptional()
  memberId?: string;

  @ApiProperty({ description: 'Mã đơn' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'Trạng thái' })
  @IsOptional()
  status?: NSOrder.EStatus;

  @ApiProperty({ description: 'Trạng thái thanh toán' })
  @IsOptional()
  paymentStatus?: NSOrder.EPaymentStatus;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> thanh toán từ' })
  @IsOptional()
  paymentDateFrom?: Date;

  @ApiProperty({ description: 'Ngày thanh toán đến' })
  @IsOptional()
  paymentDateTo?: Date;

  @ApiProperty({ description: 'ID của gói dịch vụ' })
  @IsOptional()
  packagePlanId?: string;
}

export class PlanDto {
  @ApiProperty()
  @IsUUID()
  packagePlanId: string;

  @ApiProperty()
  @IsNumber()
  price: number;

  @ApiProperty({ enum: NSPackage.EPlanTypePayment })
  @IsEnum(NSPackage.EPlanTypePayment)
  unit: string

  @ApiProperty()
  @IsNumber()
  quantity: number = 1;
}

export class CreateOrderDto {
  @ApiProperty()
  @IsNumber()
  totalPrice: number;

  @ApiProperty({ enum: NSPayment.EPaymentProvider })
  @IsEnum(NSPayment.EPaymentProvider)
  paymentProvider: NSPayment.EPaymentProvider;

  @ApiProperty({ enum: NSOrder.EPaymentMethod })
  @IsEnum(NSOrder.EPaymentMethod)
  paymentMethod: NSOrder.EPaymentMethod;

  @ApiProperty({ type: () => [PlanDto] })
  @IsNotEmpty()
  plans: PlanDto[];
}

export class UpdatePriceDto {
  @ApiProperty()
  @IsUUID()
  id: string;

  @ApiProperty()
  @IsNumber()
  totalPrice: number;
}
