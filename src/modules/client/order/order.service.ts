import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { CreateOrderDto, UpdatePriceDto } from './dto/order.dto';
import { BindRepo } from '~/@core/decorator';
import { MemberPackageRepo, OrderItemRepo, PackagePlanRepo, PaymentTransactionRepo } from '~/domains/primary';
import { OrderRepo } from '~/domains/primary';
import { DefTransaction } from '~/@core/decorator';
import { OrderListDto } from './dto/order.dto';
import { Between, In } from 'typeorm';
import { MemberRepo } from '~/domains/primary';
import { NSOrder } from '~/common/enums/order.enum';
import * as dayjs from 'dayjs';
import { memberSessionContext } from '../member-session.context';

@Injectable()
export class OrderService {
  constructor() {}

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(OrderItemRepo)
  private orderItemRepo: OrderItemRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @DefTransaction()
  async create(dto: CreateOrderDto) {
    const { memberId } = memberSessionContext;
    // Kiểm tra thông tin memberId
    const member = await this.memberRepo.findOne({ where: { id: memberId } });
    if (!member) throw new NotFoundException('Member not found');

    const planIds = dto.plans.map(plan => plan.packagePlanId);
    const plans = await this.packagePlanRepo.find({ where: { id: In(planIds) } });
    if (!plans || plans.length !== planIds.length) {
      throw new BadRequestException('Some package plans are not found');
    }

    // TODO Tạo order
    // Đếm order trong 1 ngày
    const today = dayjs().format('YYYY-MM-DD');
    const countOrders = await this.orderRepo.count({
      where: `DATE("createdDate") = '${today}'`,
    });
    const totalPrice = dto.plans.reduce((acc, plan) => acc + +plan.price , 0);
    const newOrders = this.orderRepo.create({
      ...dto,
      memberId,
      totalPrice,
      code: generateCodeHelper.generateSOCode(countOrders),
      status: NSOrder.EStatus.DRAFT,
      paymentStatus: NSOrder.EPaymentStatus.UNPAID,
    });
    await this.orderRepo.save(newOrders);
    const newOrderItems = dto.plans.map(plan =>
      this.orderItemRepo.create({
        ...plan,
        orderId: newOrders.id,
        price: plan.price, // plans.find((p) => p.id === plan.packagePlanId).sellPrice,
      }),
    );
    await this.orderItemRepo.save(newOrderItems);
    return {
      order: newOrders,
    };
  }

  async findAll() {
    return this.orderRepo.find();
  }

  async findPagination(body: OrderListDto) {
    const { memberId } = memberSessionContext;
    const {
      code,
      status,
      paymentStatus,
      paymentDateFrom,
      paymentDateTo,
      packagePlanId,
      ...pageRequest
    } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (status) {
      where.status = status;
    }
    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }
    if (paymentDateFrom && paymentDateTo) {
      where.paymentDate = Between(paymentDateFrom, paymentDateTo);
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    const { data, total } = await this.orderRepo.findPagination(
      { where, order: { createdDate: 'DESC' } },
      pageRequest,
    );
    const packages = await this.packagePlanRepo.find();
    const mapping = data.map(async item => {
      const items = await this.orderItemRepo.find({ where: { orderId: item.id } });
      const packagePlans = items.map(i => packages.find(p => p.id === i.packagePlanId));
      return {
        ...item,
        items: items.map(i => ({
          ...i,
          packagePlan: packagePlans.find(p => p.id === i.packagePlanId),
        })),
      };
    });
    return {
      data: await Promise.all(mapping),
      total,
    };
  }

  async findOne(id: string) {
    const order = await this.orderRepo.findOne({ where: { id } });
    if (!order) throw new NotFoundException('Order not found');
    const memberPackage = await this.memberPackageRepo.findOne({ where: { orderId: order.id } });
    const packages = await this.packagePlanRepo.findOne({
      where: { id: memberPackage.packagePlanId },
    });

    // payment-transaction mapping
    const paymentTransactions = await this.paymentTransactionRepo.find({
      where: { orderId: order.id },
    });
    return {
      ...order,
      item: {
        ...memberPackage,
        packageName: packages.name,
        packageCode: packages.code,
      },
      paymentTransactions,
    };
  }

  async updatePrice(body: UpdatePriceDto) {
    const { id, totalPrice } = body;
    const orders = await this.orderRepo.findOne({ where: { id } });
    if (!orders) throw new NotFoundException('Order not found');
    return await this.orderRepo.update({ id }, { totalPrice });
    
  }
}
