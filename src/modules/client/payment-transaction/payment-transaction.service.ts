import { Injectable } from "@nestjs/common";
import { BindRepo, DefTransaction } from "~/@core/decorator";
import { PaymentTransactionRepo } from "~/domains/primary/payment-transaction/payment-transaction.repo";
import { CreatePaymentTransactionDto, PaymentIntentDto, PaymentSubscriptionDto, PaymentTransactionListDto } from "./dto/payment-transaction.dto";
import Stripe from 'stripe';
import { configEnv } from "~/@config/env";
import { NSPayment } from "~/common/enums/payment.enum";
import { OrderRepo } from "~/domains/primary/order/order.repo";
import { NotFoundException } from "@nestjs/common";
import { generateCodeHelper } from "~/common/helpers/generate-code.helper";
import { OrderItemRepo, PackagePlanRepo } from "~/domains/primary/";
import { MemberRepo } from "~/domains/primary/member/member.repo";
import { MemberPackageRepo } from "~/domains/primary/member-package/member-package.repo";
import { In } from "typeorm";
import { memberSessionContext } from "../member-session.context";
import { NSOrder, NSPackage } from "~/common/enums";

@Injectable()
export class PaymentTransactionService {
    private stripe: Stripe
    constructor(
    ) { this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {}); }

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(OrderItemRepo)
    private orderItemsRepo: OrderItemRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @DefTransaction()
    async create(dto: CreatePaymentTransactionDto) {
        const { memberId } = memberSessionContext;
        const { orderId, paymentProvider, isAutoRenewal, amount, unit, timeRegister } = dto;
        const [order, planPackage, member] = await Promise.all([
            this.orderRepo.findOne({ where: { id: orderId } }),
            this.orderItemsRepo.findOne({ where: { orderId } }),
            this.memberRepo.findOne({ where: { id: memberId } }),
        ]);

        if (!order) throw new NotFoundException('Order not found');
        if (!planPackage) throw new NotFoundException('Plan package not found');
        if (!member) throw new NotFoundException('Member not found');

        const plan = await this.packagePlanRepo.findOne({ where: { id: planPackage.packagePlanId } });
        if (!plan) throw new NotFoundException('Plan not found');

        const count = await this.paymentTransactionRepo.count();

        // 2. Tạo payment transaction
        const transaction = this.paymentTransactionRepo.create({
            ...dto,
            memberId,
            code: generateCodeHelper.generateSOCode(count, 'PT'),
        });
        const savedTransaction = await this.paymentTransactionRepo.save(transaction);
        const transactionId = savedTransaction.id;

        await this.orderRepo.update({ id: orderId }, { status: NSOrder.EStatus.PENDING, totalPrice: amount });

        // 3. Tạo member package
        let newMemberPackage = null;
        const memberPackage = this.memberPackageRepo.create({
            memberId,
            orderId,
            packagePlanId: plan.id,
            initialTransactionLimit: plan.transactionLimit,
            currentTransaction: 0,
            initialConfigLimit: plan.configLimit,
            currentConfig: 0,
            status: 'PENDING',
        });
        newMemberPackage = await this.memberPackageRepo.save(memberPackage);

        // 4. Nếu Stripe thì tạo checkout session
        if (paymentProvider === NSPayment.EPaymentProvider.STRIPE) {
            if (isAutoRenewal) {
                await this.orderRepo.update({ id: orderId }, { isAutoRenewal: true });
            }

            // Tạo checkout session
            const checkoutSession = await this.createStripeCheckoutSession({
                orderId,
                transactionId,
                customerEmail: member.email,
                planId: planPackage.packagePlanId,
                paymentType: unit,
                memberPackageId: newMemberPackage.id,
                isAutoRenewal,
                amount: +amount * 100,
                timeRegister,
            });

            // Cập nhật lại transaction với sessionId
            await this.paymentTransactionRepo.update(transactionId, {
                refId: checkoutSession.sessionId,
            });

            return {
                ...savedTransaction,
                memberPackage: newMemberPackage,
                checkoutUrl: checkoutSession.checkoutUrl,
                sessionId: checkoutSession.sessionId,
            };
        }
    }

    async findAll() {
        const { memberId } = memberSessionContext;
        return this.paymentTransactionRepo.find({ where: { memberId } });
    }

    async findOne(id: string) {
        return this.paymentTransactionRepo.findOne(id);
    }

    async findPagination(body: PaymentTransactionListDto) {
        const { code, memberId, orderId, status, ...pageRequest } = body;
        const where: any = {}
        if (code) {
            where.code = code;
        }
        if (memberId) {
            where.memberId = memberId; // Update lại dùng memberContextSession 
        }
        if (orderId) {

            where.orderId = orderId;
        }
        if (status) {
            where.status = status;
        }
        return this.paymentTransactionRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
    }

    /**
     * Tạo Stripe Checkout Session cho cả payment và subscription
     */
    async createStripeCheckoutSession(body: PaymentSubscriptionDto) {
        const {
            orderId,
            transactionId,
            customerEmail,
            planId,
            paymentType,
            memberPackageId,
            isAutoRenewal,
            amount,
            timeRegister,
        } = body;

        const { REDIRECT_URL_PAYMENT } = configEnv();

        // 1. Lấy thông tin gói dịch vụ
        const plan = await this.packagePlanRepo.findOne({ where: { id: planId } });
        if (!plan) throw new NotFoundException('Plan not found');

        // 2. Xác định priceId
        const priceId = paymentType === NSPackage.EPlanTypePayment.MONTHLY
            ? plan.stripePriceMonthId
            : plan.stripePriceYearId;

        const metaData = {
            orderId,
            transactionId,
            memberPackageId,
            planId,
            timeRegister: String(timeRegister || 1),
        };

        // 3. Tạo phiên Checkout
        let session: Stripe.Checkout.Session;
        const mode = isAutoRenewal ? 'subscription' : 'payment';
        if (mode === 'subscription') {
            if (timeRegister > 1) {
                // Thanh toán 1 lần
                session = await this.stripe.checkout.sessions.create({
                    mode: 'payment',
                    customer_email: customerEmail,
                    line_items: [{
                        price_data: {
                            currency: 'usd',
                            product_data: {
                                name: `${plan.name} gói ${timeRegister} tháng`,
                            },
                            unit_amount: amount,
                        },
                        quantity: 1,
                    }],
                    success_url: `${REDIRECT_URL_PAYMENT}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
                    cancel_url: `${REDIRECT_URL_PAYMENT}`,
                    metadata: metaData,
                });
                // Tạo subscription
                const subscription = await this.stripe.subscriptions.create({
                    customer: session.customer as string,
                    items: [{
                        price: priceId,
                    }],
                    metadata: metaData,
                });
                // Cập nhật lại order với subscriptionId
                await this.orderRepo.update({ id: orderId }, { subscriptionId: subscription.id });
            } else {
                // Tự động gia hạn
                session = await this.stripe.checkout.sessions.create({
                    mode: 'subscription',
                    customer_email: customerEmail,
                    line_items: [{
                        price: priceId,
                        quantity: 1,
                    }],
                    success_url: `${REDIRECT_URL_PAYMENT}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
                    cancel_url: `${REDIRECT_URL_PAYMENT}`,
                    metadata: {
                        orderId,
                        transactionId,
                        memberPackageId,
                        planId,
                        timeRegister: String(timeRegister || 1),
                    },
                });
            }
        } else {
            // Thanh toán 1 lần
            session = await this.stripe.checkout.sessions.create({
                mode: 'payment',
                customer_email: customerEmail,
                line_items: [{
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: `${plan.name} gói ${timeRegister} tháng`,
                        },
                        unit_amount: amount,
                    },
                    quantity: 1,
                }],
                success_url: `${REDIRECT_URL_PAYMENT}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
                cancel_url: `${REDIRECT_URL_PAYMENT}`,
                metadata: metaData,
            });
        }
        return {
            checkoutUrl: session.url,
            sessionId: session.id,
        };
    }
}
