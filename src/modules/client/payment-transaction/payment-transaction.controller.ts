import { DefController } from "~/@core/decorator";
import { DefPost } from "~/@core/decorator";
import { Body } from "@nestjs/common";
import { CreatePaymentTransactionDto, PaymentTransactionListDto } from "./dto/payment-transaction.dto";
import { PaymentTransactionService } from "./payment-transaction.service";

@DefController('payment')
export class PaymentTransactionController {
    constructor(private readonly service: PaymentTransactionService) { }

    @DefPost("", {
        summary: 'Tạo mới một giao dịch thanh toán',
    })
    create(@Body() dto: CreatePaymentTransactionDto) {
        return this.service.create(dto);
    }

    @DefPost('detail', {
        summary: 'L<PERSON>y thông tin chi tiết giao dịch thanh toán',
    })
    findOne(@Body() body: string) {
        return this.service.findOne(body);
    }

    @DefPost('pagination', {
        summary: '<PERSON><PERSON><PERSON> danh sách giao dịch thanh toán của 1 member',
    })
    findPagination(@Body() body: PaymentTransactionListDto) {
        return this.service.findPagination(body);
    }

    // Find all
    @DefPost('find-all', {
        summary: 'Lấy danh sách giao dịch thanh toán của 1 member',
    })
    findAll() {
        return this.service.findAll();
    }
}