import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSConfig } from '~/common/enums/config.enum';

export class ConfigPackageListDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Mã cấu hình' })
    @IsOptional()
    code?: string;

    @ApiPropertyOptional({ description: 'Tên cấu hình' })
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsOptional()
    status?: NSConfig.EStatus;
}

export class CreateConfigPackageDto {
    @ApiProperty({ description: 'ID của gói dịch vụ member đã mua' })
    @IsNotEmpty()
    packagePlanId: string;

    @ApiProperty({ description: 'Mã cấu hình (dùng cho truy vấn nhanh)' })
    code: string;

    @ApiProperty({ description: 'Tên cấu hình' })
    name: string;

    @ApiPropertyOptional({ description: 'Mô tả cấu hình' })
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsEnum(NSConfig.EStatus)
    status: NSConfig.EStatus;

    @ApiProperty({ type: () => [CreateConfigPackageDetailDto], description: 'Danh sách field' })
    fields: CreateConfigPackageDetailDto[];
}

export class CreateConfigPackageDetailDto {
    @ApiProperty({ description: 'Tên field' })
    nameField: string;

    @ApiProperty({ description: 'Key field được mapping' })
    mappingField: string;

    @ApiProperty({ enum: NSConfig.ETypeField, description: 'Kiểu dữ liệu' })
    type: string;

    @ApiPropertyOptional({ description: 'Ghi chú hoặc mô tả field' })
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ description: 'Bắt buộc' })
    @IsOptional()
    isRequired?: boolean;
}

export class UpdateConfigPackageDto {
    @ApiProperty({ description: 'ID cấu hình' })
    @IsNotEmpty()
    id: string;

    @ApiPropertyOptional()
    @IsOptional()
    name?: string;

    @ApiPropertyOptional()
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ type: () => [CreateConfigPackageDetailDto] })
    details?: CreateConfigPackageDetailDto[];
}

export class ConfigLogDto extends PageRequest {
    @ApiProperty({ description: 'ID cấu hình' })
    @IsNotEmpty()
    configId: string;
}