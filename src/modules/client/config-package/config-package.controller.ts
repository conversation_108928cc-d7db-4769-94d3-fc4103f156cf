import {
    <PERSON>,
    Param,
    Res,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ConfigPackageService } from './config-package.service';
import { ConfigLogDto, ConfigPackageListDto, CreateConfigPackageDto, UpdateConfigPackageDto } from './dto/config-package.dto';
import { DefController, DefPost } from '~/@core/decorator';

@ApiTags('Config Package API')
@DefController('config-package')
export class ConfigPackageController {
    constructor(private readonly service: ConfigPackageService) { }

    @DefPost("", {
        summary: 'Tạo mới một cấu hình',
    })
    create(@Body() dto: CreateConfigPackageDto) {
        return this.service.create(dto);
    }

    @DefPost('pagination', {
        summary: '<PERSON><PERSON><PERSON> danh sách cấu hình',
    })
    findPagination(@Body() body: ConfigPackageListDto) {
        return this.service.findPagination(body);
    }

    @DefPost('find-all', {
        summary: 'L<PERSON>y danh sách cấu hình của member',
    })
    findAll() {
        return this.service.findAll();
    }

    @DefPost('logs', {
        summary: 'Lấy danh sách log của cấu hình',
    })
    findLogs(@Body() body: ConfigLogDto) {
        return this.service.findLogs(body);
    }

    @DefPost('detail', {
        summary: 'Lấy thông tin chi tiết cấu hình',
    })
    findOne(@Body('id') id: string) {
        return this.service.findOne(id);
    }

    @DefPost('update', {
        summary: 'Cập nhật cấu hình',
    })
    update(@Body() body: UpdateConfigPackageDto) {
        return this.service.update(body);
    }

    @DefPost('inactive', {
        summary: 'Đổi trạng thái cấu hình về Inactive',
    })
    inActive(@Body() body: { id: string }) {
        const { id } = body;
        return this.service.inActive(id);
    }

    @DefPost('active', {
        summary: 'Đổi trạng thái cấu hình về Active',
    })
    active(@Body() body: { id: string }) {
        const { id } = body;
        return this.service.active(id);
    }

    @DefPost('generate-api', {
        summary: 'Tạo cấu hình API cho thành viên',
    })
    generateMemberAPIConfig(@Body() body: { id: string }) {
        const { id } = body;
        return this.service.generateMemberAPIConfig(id);
    }

    // TODO Tạm thời để controller này
    @DefPost('external/:configId', {
        summary: 'Xử lý request từ phía external',
    })
    async handleIncomingExternalApi(@Body() body: any, @Param('configId') id: string) {
        return await this.service.handleIncomingExternalApi(body, id);
    }
}
