import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { RequestContext } from '~/@core/context';
import { KEY_HEADER, KEY_SESSION_CONTEXT } from '~/common/constants';
import { MemberSessionDto } from '~/modules/client/member-auth/dto';
import { nanoid } from 'nanoid';
import { MemberLoginReq } from '~/modules/client/member-auth/dto';

@Injectable()
export class MemberMiddleware implements NestMiddleware {
  constructor(
    private jwtService: JwtService,
  ) { }
  async use(req: Request, res: Response, next: Function) {
    const { JWT_SECRET } = configEnv();
    try {
      const { headers = {} } = req;
      if (!headers || !headers[KEY_HEADER.AUTHORIZATION]) {
        throw new UnauthorizedException('Unauthorized');
      }
      const accessTokenBearer = headers[KEY_HEADER.AUTHORIZATION] as string;
      const accessToken = accessTokenBearer.replace('Bearer', '').trim();

      if (!accessToken) {
        throw new UnauthorizedException('Unauthorized');
      }

      try {
        const payload = await this.jwtService.verifyAsync<MemberLoginReq>(accessToken, {
          secret: JWT_SECRET,
        });
        RequestContext.setAttribute<MemberSessionDto>(KEY_SESSION_CONTEXT.MEMBER_SESSION, {
          accessToken,
          refreshToken: '',
          tokenType: 'Bearer',
          ...payload,
        });
        next();
      } catch (error) {
        console.log(`==========`, error);
        throw new UnauthorizedException('Unauthorized');
      }
    } catch (error) {
      console.log(error);
      
      next(new UnauthorizedException('Unauthorized'));
    }
  }
}


@Injectable()
export class SessionMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: Function) {
    if (!req.cookies?.sessionId) {
      const sessionId = nanoid();
      res.cookie('sessionId', sessionId, {
        httpOnly: true,
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 ngày
        sameSite: 'lax',
      });
      req['sessionId'] = sessionId;
    } else {
      req['sessionId'] = req.cookies.sessionId;
    }
    next();
  }
}