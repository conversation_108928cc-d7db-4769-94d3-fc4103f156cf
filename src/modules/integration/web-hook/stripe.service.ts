import { HttpStatus, Injectable, NotFoundException, Req, Res } from "@nestjs/common";
import { Request, Response } from "express";
import { BindRepo } from "~/@core/decorator";
import { OrderRepo, PaymentTransactionRepo, PackagePlanRepo, MemberPackageRepo } from "~/domains/primary";
import Stripe from 'stripe'
import { configEnv } from "~/@config/env";
import { NSOrder } from "~/common/enums/order.enum";
import { NSPackage } from "~/common/enums/package.enum";
import { NSPayment } from "~/common/enums/payment.enum";
import * as dayjs from 'dayjs';
import { NSMember } from "~/common/enums";

@Injectable()
export class StripeService {
    private stripe: Stripe
    constructor(
    ) {
        this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {
        })
    }

    @BindRepo(OrderRepo)
    orderRepo: OrderRepo;

    @BindRepo(PaymentTransactionRepo)
    paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(MemberPackageRepo)
    private memberPackageRepo: MemberPackageRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    async handleWebhook(@Req() req: Request, @Res() res: Response) {
        const sig = req.headers['stripe-signature'];
        if (!sig) {
            return res.status(HttpStatus.BAD_REQUEST).send('No signature found')
        }
        const webhookSecret = configEnv().STRIPE_WEBHOOK_SECRET;
        let event: Stripe.Event;
        try {
            event = this.stripe.webhooks.constructEvent(
                req.body,         // ⚠️ MUST be raw body (buffer)
                sig as string,
                webhookSecret
            );
        } catch (err) {
            console.error('Webhook signature verification failed.', err.message)
            return res.status(HttpStatus.BAD_REQUEST).send(`Webhook Error: ${err.message}`)
        }

        const data = event.data.object as Stripe.PaymentIntent;
        const { memberPackageId, orderId, transactionId, paymentType } = data.metadata

        // Lấy ra subscriptionId nếu có
        let subscriptionId = '';
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object as Stripe.Checkout.Session;
            subscriptionId = session.subscription as string;
        }

        switch (event.type) {
            case 'payment_intent.succeeded':
            case 'invoice.payment_succeeded':
                await this.updateOrderPaymentStatus(orderId, transactionId, NSOrder.EPaymentStatus.PAID, paymentType, memberPackageId);
                break
            case 'payment_intent.payment_failed':
            case 'invoice.payment_failed':
                await this.updateOrderPaymentStatus(orderId, transactionId, NSOrder.EPaymentStatus.UNPAID, paymentType, memberPackageId);
                break

            case 'checkout.session.completed':
            case 'checkout.session.async_payment_succeeded':
                await this.updateOrderPaymentStatus(orderId, transactionId, NSOrder.EPaymentStatus.PAID, paymentType, memberPackageId, subscriptionId);
                break
            case 'checkout.session.async_payment_failed':
                await this.updateOrderPaymentStatus(orderId, transactionId, NSOrder.EPaymentStatus.UNPAID, paymentType, memberPackageId);
                break
            default:
                break
        }

        return res.status(HttpStatus.OK).send({
            message: 'success',
            data: data?.metadata,
        })
    }

    async updateOrderPaymentStatus(
        orderId: string,
        paymentTransactionId: string,
        paymentStatus: NSOrder.EPaymentStatus,
        paymentType: string,
        memberPackageId: string,
        subscriptionId?: string,
    ) {
        await this.orderRepo.update({
            id: orderId,
        }, {
            paymentStatus,
            status: paymentStatus === NSOrder.EPaymentStatus.PAID
                ? NSOrder.EStatus.COMPLETED
                : NSOrder.EStatus.PENDING,
            paymentDate: new Date(),
            subscriptionId,
        });
        await this.paymentTransactionRepo.update({
            id: paymentTransactionId,
        }, {
            status: paymentStatus === NSOrder.EPaymentStatus.PAID
                ? NSPayment.ETransactionStatus.COMPLETED
                : NSPayment.ETransactionStatus.FAILED,
            transactionDate: new Date(),
        });

        if (paymentStatus === NSOrder.EPaymentStatus.PAID) {
            const pkg = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });
            if (!pkg) throw new NotFoundException('Member package not found');

            const plan = await this.packagePlanRepo.findOne({ where: { id: pkg.packagePlanId } });
            if (!plan) throw new NotFoundException('Plan not found');

            const unit = paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year';
            const expiredDate = dayjs().add(1, unit).format('YYYY-MM-DD');

            await this.memberPackageRepo.update({
                id: memberPackageId,
            }, {
                status: NSMember.EMemberPackageStatus.ACTIVE,
                expiredDate,
                activatedDate: new Date(),
            });
        }
    }
}