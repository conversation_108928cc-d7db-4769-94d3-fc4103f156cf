import { <PERSON>, Param, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ConfigPackageService } from './config-package.service';
import {
  ConfigLogDto,
  ConfigPackageListDto,
  CreateConfigPackageDto,
} from './dto/config-package.dto';
import { DefController, DefPost } from '~/@core/decorator';

@ApiTags('Config Package API')
@DefController('config-package')
export class ConfigPackageController {
  constructor(private readonly service: ConfigPackageService) {}

  @DefPost('pagination', {
    summary: '<PERSON><PERSON>y danh sách cấu hình',
  })
  findPagination(@Body() body: ConfigPackageListDto) {
    return this.service.findPagination(body);
  }

  @DefPost('logs', {
    summary: 'Lấy danh sách log của cấu hình',
  })
  findLogs(@Body() body: ConfigLogDto) {
    return this.service.findLogs(body);
  }

  @DefPost('detail', {
    summary: '<PERSON><PERSON>y thông tin chi tiết cấu hình',
  })
  findOne(@Body('id') id: string) {
    return this.service.findOne(id);
  }
}
