import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import {
  ConfigLogDto,
  ConfigPackageListDto,
  CreateConfigPackageDto,
  UpdateConfigPackageDto,
} from './dto/config-package.dto';
import {
  ConfigPackageRepo,
  ConfigPackageDetailRepo,
} from '~/domains/primary/config-package/config-package.repo';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSConfig } from '~/common/enums/config.enum';
import { ConfigPackageDetailEntity } from '~/domains/primary/config-package/config-package-detail.entity';
import * as dayjs from 'dayjs';
import { MemberApiRepo } from '~/domains/primary/member-api/member-api.repo';
import * as _ from 'lodash';
import { generateCurlText, generatePayloadFields } from '~/common/helpers/generate-text.helper';
import { configEnv } from '~/@config/env';
import { MemberApiLogRepo } from '~/domains/primary/member-api-log/member-api-log.repo';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import { BusinessException } from '~/@systems/exceptions';
import { memberSessionContext } from '~/modules/client/member-session.context';

@Injectable()
export class ConfigPackageService {
  constructor() {}

  @BindRepo(ConfigPackageRepo)
  private configRepo: ConfigPackageRepo;

  @BindRepo(ConfigPackageDetailRepo)
  private detailRepo: ConfigPackageDetailRepo;

  @BindRepo(MemberApiRepo)
  private memberApiRepo: MemberApiRepo;

  @BindRepo(MemberApiLogRepo)
  private memberApiLogRepo: MemberApiLogRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  async findPagination(body: ConfigPackageListDto) {
    const { memberId } = memberSessionContext;
    const { code, name, status, ...pageRequest } = body;
    const where: any = { memberId };
    if (code) {
      where.code = code;
    }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    return this.configRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const config = await this.configRepo.findOne(id);
    if (!config) throw new NotFoundException('Config package not found');
    const details = await this.detailRepo.find({ where: { configPackageId: config.id } });

    const apiInfo = await this.memberApiRepo.findOne({ where: { configId: config.id } });
    return {
      ...config,
      apiInfo,
      fields: details || [],
    };
  }

  async findLogs(body: ConfigLogDto) {
    const { configId, ...pageRequest } = body;
    return this.memberApiLogRepo.findPagination(
      { where: { configId: body.configId } },
      pageRequest,
    );
  }
}
