import { Injectable, NotFoundException } from '@nestjs/common';
import { CreatePackagePlanDto, UpdatePackagePlanDto, PackagePlanListDto } from './dto/package.dto';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';
import { Between } from 'typeorm';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';

@Injectable()
export class PackageService {
  private stripe: Stripe
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @DefTransaction()
  async create(dto: CreatePackagePlanDto) {
    const count = await this.packagePlanRepo.count();
    const planCode = generateCodeHelper.generateCode('APE', count + 1);
    const pkg = this.packagePlanRepo.create({
      ...dto,
      code: planCode,
    });

    //#region Tạo ID giá áp dụng thanh toán tự động
    const { STRIPE_PRODUCT_SUBSCRIPTION_ID } = configEnv();
    const productId = STRIPE_PRODUCT_SUBSCRIPTION_ID; // product chung cho các gói
    const priceMonth = await this.stripe.prices.create({
        unit_amount: +dto.sellPriceMonthly * 100,
        currency: "usd",
        recurring: {
            interval:  'month',
            interval_count: 1, // Số lượng tháng
        },
        product: productId,
    });
    pkg.stripePriceMonthId = priceMonth.id;
    const priceYear = await this.stripe.prices.create({
        unit_amount: +dto.sellPriceYearly * 100,
        currency: "usd",
        recurring: {
            interval:  'year',
            interval_count: 1, // Số lượng năm
        },
        product: productId,
    });
    pkg.stripePriceYearId = priceYear.id;
    //#endregion

    return this.packagePlanRepo.save(pkg);
  }

  async findAll() {
    return this.packagePlanRepo.find();
  }

  async findPagination(body: PackagePlanListDto) {
    const { code, memberId, name, status, createdDateFrom, createdDateTo, ...pageRequest } = body;
    const where: any = {};
    if (name) {
      where.name = name;
    }
    //code
    if (code) {
      where.code = code;
    }
    //memberId
    if (memberId) {
      where.memberId = memberId;
    }
    if (status) {
      where.status = status;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    return await this.packagePlanRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const pkg = await this.packagePlanRepo.findOne({ where: { id } });
    if (!pkg) throw new NotFoundException(`Package ${id} not found`);
    return pkg;
  }

  @DefTransaction()
  async update(body: UpdatePackagePlanDto) {
    const { id, ...dto } = body;
    const pkg = await this.findOne(id);
    return this.packagePlanRepo.update(
      {
        id,
      },
      {
        ...pkg,
        ...dto,
      },
    );
  }

  @DefTransaction()
  async inActive(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new NotFoundException(`Package ${id} is most popular`);
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.INACTIVE,
      },
    );
  }

  @DefTransaction()
  async active(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new NotFoundException(`Package ${id} is most popular`);
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.ACTIVE,
      },
    );
  }
}
