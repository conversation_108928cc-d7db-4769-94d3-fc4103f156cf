import { Injectable, NotFoundException } from "@nestjs/common";
import { BindRepo } from "~/@core/decorator";
import { PaymentTransactionRepo } from "~/domains/primary/payment-transaction/payment-transaction.repo";
import { PaymentTransactionListDto } from "~/modules/client/payment-transaction/dto/payment-transaction.dto";
import { MemberRepo } from "~/domains/primary";
import { In } from "typeorm";
import { OrderRepo } from "~/domains/primary";
import { OrderItemRepo } from "~/domains/primary";
import { PackagePlanRepo } from "~/domains/primary";

@Injectable()
export class PaymentService {
    constructor(
    ) { }

    @BindRepo(PaymentTransactionRepo)
    private paymentTransactionRepo: PaymentTransactionRepo;

    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(OrderRepo)
    private orderRepo: OrderRepo;

    @BindRepo(OrderItemRepo)
    private orderItemRepo: OrderItemRepo;

    @BindRepo(PackagePlanRepo)
    private packagePlanRepo: PackagePlanRepo;

    async findOne(id: string) {
        const payment = await this.paymentTransactionRepo.findOne(id);
        if (!payment) throw new NotFoundException('Payment not found');

        const member = await this.memberRepo.findOne({ where: { id: payment.memberId } });
        if (!member) throw new NotFoundException('Member not found');

        const order = await this.orderRepo.findOne({ where: { id: payment.orderId } });
        if (!order) throw new NotFoundException('Order not found');

        const items = await this.orderItemRepo.find({ where: { orderId: order.id } });
        const packages = await this.packagePlanRepo.find({ where: { id: In(items.map((item) => item.packagePlanId)) } });
        
        return {
            payment,
            member,
            order: {
                ...order,
                items: {
                    ...items,
                    packages,
                },
                
            }
        };
    }

    async findPagination(body: PaymentTransactionListDto) {
        const { code, memberId, orderId, status, ...pageRequest } = body;
        const where: any = {}
        if (code) {
            where.code = code;
        }
        if (memberId) {
            where.memberId = memberId;
        }
        if (orderId) {
            where.orderId = orderId;
        }
        if (status) {
            where.status = status;
        }
        const { data, total } = await this.paymentTransactionRepo.findPagination({ where, order: { createdDate: 'DESC' } }, pageRequest);
        const members = await this.memberRepo.find();

        // Mapping lấy thông tin member
        const mapping = data.map((item) => {
            const member = members.find((m) => m.id === item.memberId);
            return {
                ...item,
                member,
            };
        });

        return {
            data: mapping,
            total,
        };
    }
}